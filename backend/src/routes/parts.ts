import express from 'express';
import { pgPool, esClient } from '../config/database';
import { ElasticsearchService } from '../services/elasticsearchService';
import { Part, MatchResult } from '../types';

const router = express.Router();
const esService = new ElasticsearchService();

// Get all parts with optional filtering (uses Elasticsearch for search)
router.get('/', async (req, res) => {
  try {
    const {
      list_id,
      contact_id,
      list_type,
      search,
      manufacturer,
      condition,
      page = 1,
      limit = 50,
      fuzziness = 'AUTO'
    } = req.query;

    // If there's a search query, use Elasticsearch for fuzzy search
    if (search && typeof search === 'string' && search.trim()) {
      try {
        const searchResult = await esService.fuzzySearch({
          query: search.trim(),
          manufacturer: manufacturer as string,
          list_type: list_type as 'offer' | 'needs' | 'using',
          contact_id: contact_id as string,
          condition: condition as string,
          page: Number(page),
          limit: Number(limit),
          fuzziness: fuzziness as string
        });

        return res.json({
          parts: searchResult.parts,
          pagination: {
            page: searchResult.page,
            limit: searchResult.limit,
            total: searchResult.total,
            pages: searchResult.pages
          },
          aggregations: searchResult.aggregations,
          search_type: 'elasticsearch_fuzzy'
        });
      } catch (esError) {
        console.error('Elasticsearch search failed, falling back to PostgreSQL:', esError);
        // Fall through to PostgreSQL search
      }
    }

    let query = `
      SELECT p.*, l.list_type, l.filename, c.name as contact_name, c.company as contact_company
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      WHERE 1=1
    `;

    const params: any[] = [];
    let paramCount = 0;

    if (list_id) {
      paramCount++;
      query += ` AND p.list_id = $${paramCount}`;
      params.push(String(list_id));
    }

    if (contact_id) {
      paramCount++;
      query += ` AND l.contact_id = $${paramCount}`;
      params.push(String(contact_id));
    }

    if (list_type) {
      paramCount++;
      query += ` AND l.list_type = $${paramCount}`;
      params.push(String(list_type));
    }

    if (search) {
      paramCount++;
      query += ` AND (p.part_number ILIKE $${paramCount} OR p.description ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (manufacturer) {
      paramCount++;
      query += ` AND p.manufacturer ILIKE $${paramCount}`;
      params.push(`%${manufacturer}%`);
    }

    if (condition) {
      paramCount++;
      query += ` AND p.condition = $${paramCount}`;
      params.push(String(condition));
    }

    query += ` ORDER BY p.created_at DESC`;

    // Add pagination
    const offset = (Number(page) - 1) * Number(limit);
    paramCount++;
    query += ` LIMIT $${paramCount}`;
    params.push(Number(limit));

    paramCount++;
    query += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pgPool.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      WHERE 1=1
    `;

    const countParams = params.slice(0, -2); // Remove limit and offset
    let countParamCount = 0;

    if (list_id) {
      countParamCount++;
      countQuery += ` AND p.list_id = $${countParamCount}`;
    }

    if (contact_id) {
      countParamCount++;
      countQuery += ` AND l.contact_id = $${countParamCount}`;
    }

    if (list_type) {
      countParamCount++;
      countQuery += ` AND l.list_type = $${countParamCount}`;
    }

    if (search) {
      countParamCount++;
      countQuery += ` AND (p.part_number ILIKE $${countParamCount} OR p.description ILIKE $${countParamCount})`;
    }

    if (manufacturer) {
      countParamCount++;
      countQuery += ` AND p.manufacturer ILIKE $${countParamCount}`;
    }

    if (condition) {
      countParamCount++;
      countQuery += ` AND p.condition = $${countParamCount}`;
    }

    const countResult = await pgPool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      parts: result.rows,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching parts:', error);
    res.status(500).json({ error: 'Failed to fetch parts' });
  }
});

// Get specific part
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT p.*, l.list_type, l.filename, c.name as contact_name, c.company as contact_company
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      WHERE p.id = $1
    `;

    const result = await pgPool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Part not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching part:', error);
    res.status(500).json({ error: 'Failed to fetch part' });
  }
});

// Enhanced MPN search with better matching
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const {
      list_type,
      manufacturer,
      condition,
      page = 1,
      limit = 20
    } = req.query;

    let searchQuery = `
      SELECT p.*, l.list_type, l.filename, c.name as contact_name, c.company as contact_company,
             CASE
               WHEN UPPER(p.part_number) = UPPER($1) THEN 100
               WHEN p.part_number ILIKE $1 THEN 90
               WHEN p.part_number ILIKE $2 THEN 80
               WHEN p.description ILIKE $2 THEN 60
               WHEN p.manufacturer ILIKE $2 THEN 40
               ELSE 20
             END as match_score
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      WHERE (p.part_number ILIKE $2 OR p.description ILIKE $2 OR p.manufacturer ILIKE $2)
    `;

    const params: any[] = [query, `%${query}%`];
    let paramCount = 2;

    if (list_type) {
      paramCount++;
      searchQuery += ` AND l.list_type = $${paramCount}`;
      params.push(String(list_type));
    }

    if (manufacturer) {
      paramCount++;
      searchQuery += ` AND p.manufacturer ILIKE $${paramCount}`;
      params.push(`%${manufacturer}%`);
    }

    if (condition) {
      paramCount++;
      searchQuery += ` AND p.condition = $${paramCount}`;
      params.push(String(condition));
    }

    searchQuery += ` ORDER BY match_score DESC, p.part_number`;

    // Add pagination
    const offset = (Number(page) - 1) * Number(limit);
    paramCount++;
    searchQuery += ` LIMIT $${paramCount}`;
    params.push(Number(limit));

    paramCount++;
    searchQuery += ` OFFSET $${paramCount}`;
    params.push(offset);

    const result = await pgPool.query(searchQuery, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      WHERE (p.part_number ILIKE $2 OR p.description ILIKE $2 OR p.manufacturer ILIKE $2)
    `;

    const countParams = [query, `%${query}%`];
    let countParamCount = 2;

    if (list_type) {
      countParamCount++;
      countQuery += ` AND l.list_type = $${countParamCount}`;
      countParams.push(String(list_type));
    }

    if (manufacturer) {
      countParamCount++;
      countQuery += ` AND p.manufacturer ILIKE $${countParamCount}`;
      countParams.push(`%${manufacturer}%`);
    }

    if (condition) {
      countParamCount++;
      countQuery += ` AND p.condition = $${countParamCount}`;
      countParams.push(String(condition));
    }

    const countResult = await pgPool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      parts: result.rows,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error searching parts:', error);
    res.status(500).json({ error: 'Failed to search parts' });
  }
});

// Find matches for a specific part (simplified PostgreSQL version)
router.get('/:id/matches', async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.query;

    // Get the source part
    const partQuery = `
      SELECT p.*, l.list_type, l.contact_id
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      WHERE p.id = $1
    `;

    const partResult = await pgPool.query(partQuery, [id]);

    if (partResult.rows.length === 0) {
      return res.status(404).json({ error: 'Part not found' });
    }

    const sourcePart = partResult.rows[0];

    // Determine opposite list types for matching
    let targetListTypes: string[] = [];
    if (sourcePart.list_type === 'needs') {
      targetListTypes = ['offer'];
    } else if (sourcePart.list_type === 'offer') {
      targetListTypes = ['needs'];
    } else {
      // For 'using' lists, we might want to find both offers and needs
      targetListTypes = ['offer', 'needs'];
    }

    // Search for matching parts using PostgreSQL
    const matchQuery = `
      SELECT p.*, l.list_type, l.filename, c.name as contact_name, c.company as contact_company, c.email, c.phone,
             CASE
               WHEN p.part_number = $1 THEN 1.0
               WHEN p.part_number ILIKE $2 THEN 0.8
               WHEN p.description ILIKE $2 THEN 0.6
               ELSE 0.4
             END as match_score
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      WHERE l.list_type = ANY($3)
        AND l.contact_id != $4
        AND (p.part_number ILIKE $2 OR p.description ILIKE $2)
      ORDER BY match_score DESC, p.part_number
      LIMIT $5
    `;

    const matchResult = await pgPool.query(matchQuery, [
      sourcePart.part_number,
      `%${sourcePart.part_number}%`,
      targetListTypes,
      sourcePart.contact_id,
      Number(limit)
    ]);

    const matches = matchResult.rows.map(part => ({
      ...part,
      match_type: 'part_number_similarity'
    }));

    res.json({ matches });

  } catch (error) {
    console.error('Error finding matches:', error);
    res.status(500).json({ error: 'Failed to find matches' });
  }
});

export default router;
