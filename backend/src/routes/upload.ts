import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { pgPool } from '../config/database';
import { CSVProcessor, type ColumnAnalysis } from '../services/csvProcessor';
import { UploadedFile } from '../types';

// Helper function to convert BigInt values to numbers for JSON serialization
function sanitizeForJSON(obj: any): any {
  return JSON.parse(JSON.stringify(obj, (key, value) =>
    typeof value === 'bigint' ? Number(value) : value
  ));
}

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept CSV, Excel, and text files
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only CSV, Excel, and text files are allowed.'));
    }
  },
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  }
});

// Upload file endpoint
router.post('/', (req, res, next) => {
  upload.single('file')(req, res, (err) => {
    if (err) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'File too large. Maximum file size is 500MB.'
        });
      }
      if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        return res.status(400).json({
          error: 'Unexpected file field. Please use the "file" field name.'
        });
      }
      if (err.message.includes('Invalid file type')) {
        return res.status(400).json({
          error: 'Invalid file type. Only CSV, Excel, and text files are allowed.'
        });
      }
      return res.status(400).json({
        error: err.message || 'File upload error'
      });
    }
    next();
  });
}, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { contact_id, list_type } = req.body;

    if (!contact_id || !list_type) {
      return res.status(400).json({ error: 'Contact ID and list type are required' });
    }

    if (!['offer', 'needs', 'using'].includes(list_type)) {
      return res.status(400).json({ error: 'Invalid list type' });
    }

    // Save file info to database
    const query = `
      INSERT INTO lists (contact_id, filename, file_path, list_type, upload_date, processed)
      VALUES ($1, $2, $3, $4, NOW(), false)
      RETURNING *
    `;

    const result = await pgPool.query(query, [
      contact_id,
      req.file.originalname,
      req.file.path,
      list_type
    ]);

    const listId = result.rows[0].id;

    // Process CSV file immediately using DuckDB
    console.log(`Processing CSV file: ${req.file.path}`);
    const processor = new CSVProcessor();

    try {
      const processingResult = await processor.processCSVFile(req.file.path, listId);

      if (processingResult.success) {
        res.status(201).json({
          message: `File "${req.file.originalname}" uploaded and processed successfully! ${processingResult.processedParts} parts imported.`,
          list: {
            ...result.rows[0],
            processed: true,
            total_parts: processingResult.processedParts
          },
          file: {
            originalname: req.file.originalname,
            filename: req.file.filename,
            size: req.file.size,
            mimetype: req.file.mimetype
          },
          processing: {
            totalParts: processingResult.totalParts,
            processedParts: processingResult.processedParts,
            errors: processingResult.errors,
            sampleData: processingResult.sampleData
          }
        });
      } else {
        // Processing failed, but file was uploaded
        res.status(207).json({ // 207 Multi-Status
          message: `File "${req.file.originalname}" uploaded but processing failed.`,
          list: result.rows[0],
          file: {
            originalname: req.file.originalname,
            filename: req.file.filename,
            size: req.file.size,
            mimetype: req.file.mimetype
          },
          processing: {
            totalParts: processingResult.totalParts,
            processedParts: processingResult.processedParts,
            errors: processingResult.errors
          }
        });
      }
    } finally {
      processor.close();
    }

  } catch (error) {
    console.error('Error uploading file:', error);

    // Clean up uploaded file if database operation failed
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({ error: 'Failed to upload file' });
  }
});

// Get all lists
router.get('/lists', async (req, res) => {
  try {
    const query = `
      SELECT l.*, c.name as contact_name, c.company as contact_company
      FROM lists l
      JOIN contacts c ON l.contact_id = c.id
      ORDER BY l.upload_date DESC
    `;

    const result = await pgPool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching lists:', error);
    res.status(500).json({ error: 'Failed to fetch lists' });
  }
});

// Get specific list
router.get('/lists/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT l.*, c.name as contact_name, c.company as contact_company
      FROM lists l
      JOIN contacts c ON l.contact_id = c.id
      WHERE l.id = $1
    `;

    const result = await pgPool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'List not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching list:', error);
    res.status(500).json({ error: 'Failed to fetch list' });
  }
});

// Delete list and associated file
router.delete('/lists/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get file path before deleting
    const getFileQuery = 'SELECT file_path FROM lists WHERE id = $1';
    const fileResult = await pgPool.query(getFileQuery, [id]);

    if (fileResult.rows.length === 0) {
      return res.status(404).json({ error: 'List not found' });
    }

    const filePath = fileResult.rows[0].file_path;

    // Delete from database
    const deleteQuery = 'DELETE FROM lists WHERE id = $1 RETURNING *';
    const result = await pgPool.query(deleteQuery, [id]);

    // Delete physical file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.json({ message: 'List deleted successfully', list: result.rows[0] });
  } catch (error) {
    console.error('Error deleting list:', error);
    res.status(500).json({ error: 'Failed to delete list' });
  }
});

// Analyze CSV file structure (Step 1 of 2-step upload)
router.post('/analyze', (req, res, next) => {
  upload.single('file')(req, res, (err) => {
    if (err) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'File too large. Maximum file size is 500MB.'
        });
      }
      return res.status(400).json({
        error: err.message || 'File upload error'
      });
    }
    next();
  });
}, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { contact_id, list_type } = req.body;

    if (!contact_id || !list_type) {
      return res.status(400).json({ error: 'Contact ID and list type are required' });
    }

    if (!['offer', 'needs', 'using'].includes(list_type)) {
      return res.status(400).json({ error: 'Invalid list type' });
    }

    // Save file info to database (not processed yet)
    const query = `
      INSERT INTO lists (contact_id, filename, file_path, list_type, upload_date, processed)
      VALUES ($1, $2, $3, $4, NOW(), false)
      RETURNING *
    `;

    const result = await pgPool.query(query, [
      contact_id,
      req.file.originalname,
      req.file.path,
      list_type
    ]);

    // Analyze CSV structure using DuckDB
    console.log(`Analyzing CSV file: ${req.file.path}`);
    const processor = new CSVProcessor();

    try {
      const analysis = await processor.analyzeCSVFile(req.file.path);

      res.status(200).json(sanitizeForJSON({
        message: 'File uploaded and analyzed successfully',
        list: result.rows[0],
        file: {
          originalname: req.file.originalname,
          filename: req.file.filename,
          size: req.file.size,
          mimetype: req.file.mimetype
        },
        analysis
      }));
    } finally {
      processor.close();
    }

  } catch (error) {
    console.error('Error analyzing file:', error);

    // Clean up uploaded file if analysis failed
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({ error: 'Failed to analyze file' });
  }
});

// Process CSV with confirmed field mapping (Step 2 of 2-step upload)
router.post('/process/:listId', async (req, res) => {
  try {
    const { listId } = req.params;
    const { fieldMapping } = req.body;

    if (!fieldMapping || typeof fieldMapping !== 'object') {
      return res.status(400).json({ error: 'Field mapping is required' });
    }

    if (!fieldMapping.part_number) {
      return res.status(400).json({ error: 'Part number field mapping is required' });
    }

    // Get list info
    const listQuery = 'SELECT * FROM lists WHERE id = $1';
    const listResult = await pgPool.query(listQuery, [listId]);

    if (listResult.rows.length === 0) {
      return res.status(404).json({ error: 'List not found' });
    }

    const list = listResult.rows[0];

    if (list.processed) {
      return res.status(400).json({ error: 'List has already been processed' });
    }

    if (!fs.existsSync(list.file_path)) {
      return res.status(400).json({ error: 'Uploaded file not found' });
    }

    // Process CSV file with custom mapping
    console.log(`Processing CSV file with custom mapping: ${list.file_path}`);
    const processor = new CSVProcessor();

    try {
      const processingResult = await processor.processCSVFileWithMapping(
        list.file_path,
        listId,
        fieldMapping
      );

      if (processingResult.success) {
        res.status(200).json({
          message: `File "${list.filename}" processed successfully! ${processingResult.processedParts} parts imported.`,
          list: {
            ...list,
            processed: true,
            total_parts: processingResult.processedParts
          },
          processing: {
            totalParts: processingResult.totalParts,
            processedParts: processingResult.processedParts,
            errors: processingResult.errors,
            sampleData: processingResult.sampleData
          }
        });
      } else {
        res.status(400).json({
          message: `File "${list.filename}" processing failed.`,
          list,
          processing: {
            totalParts: processingResult.totalParts,
            processedParts: processingResult.processedParts,
            errors: processingResult.errors
          }
        });
      }
    } finally {
      processor.close();
    }

  } catch (error) {
    console.error('Error processing file:', error);
    res.status(500).json({ error: 'Failed to process file' });
  }
});

export default router;
