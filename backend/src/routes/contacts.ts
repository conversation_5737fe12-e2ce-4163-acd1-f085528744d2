import express from 'express';
import { pgPool } from '../config/database';
import { Contact } from '../types';

const router = express.Router();

// Get all contacts
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT c.*,
             COUNT(l.id) as total_lists,
             COUNT(p.id) as total_parts
      FROM contacts c
      LEFT JOIN lists l ON c.id = l.contact_id
      LEFT JOIN parts p ON l.id = p.list_id
      GROUP BY c.id
      ORDER BY c.name
    `;

    const result = await pgPool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({ error: 'Failed to fetch contacts' });
  }
});

// Create new contact
router.post('/', async (req, res) => {
  try {
    const { name, email, phone, company } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    const query = `
      INSERT INTO contacts (name, email, phone, company)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await pgPool.query(query, [name, email, phone, company]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating contact:', error);
    res.status(500).json({ error: 'Failed to create contact' });
  }
});

// Get specific contact by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT c.*,
             COUNT(l.id) as total_lists,
             COUNT(p.id) as total_parts
      FROM contacts c
      LEFT JOIN lists l ON c.id = l.contact_id
      LEFT JOIN parts p ON l.id = p.list_id
      WHERE c.id = $1
      GROUP BY c.id
    `;

    const result = await pgPool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Contact not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching contact:', error);
    res.status(500).json({ error: 'Failed to fetch contact' });
  }
});

// Update contact
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, phone, company } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    const query = `
      UPDATE contacts
      SET name = $1, email = $2, phone = $3, company = $4, updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `;

    const result = await pgPool.query(query, [name, email, phone, company, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Contact not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating contact:', error);
    res.status(500).json({ error: 'Failed to update contact' });
  }
});

// Delete contact
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if contact has associated lists
    const checkQuery = `
      SELECT COUNT(*) as list_count FROM lists WHERE contact_id = $1
    `;
    const checkResult = await pgPool.query(checkQuery, [id]);

    if (parseInt(checkResult.rows[0].list_count) > 0) {
      return res.status(400).json({
        error: 'Cannot delete contact with associated lists. Please delete the lists first.'
      });
    }

    const deleteQuery = `
      DELETE FROM contacts WHERE id = $1 RETURNING *
    `;

    const result = await pgPool.query(deleteQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Contact not found' });
    }

    res.json({ message: 'Contact deleted successfully' });
  } catch (error) {
    console.error('Error deleting contact:', error);
    res.status(500).json({ error: 'Failed to delete contact' });
  }
});

export default router;
