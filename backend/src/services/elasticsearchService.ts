import { esClient } from '../config/database';

export interface ElasticsearchPart {
  id: string;
  list_id: string;
  part_number: string;
  description?: string;
  manufacturer?: string;
  quantity?: number;
  unit_price?: number;
  condition?: string;
  list_type: string;
  contact_id: string;
  contact_name: string;
  contact_company?: string;
  created_at: string;
}

export interface FuzzySearchParams {
  query: string;
  manufacturer?: string;
  list_type?: 'offer' | 'needs' | 'using';
  contact_id?: string;
  min_price?: number;
  max_price?: number;
  condition?: string;
  page?: number;
  limit?: number;
  fuzziness?: string; // 'AUTO', '0', '1', '2'
}

export interface SearchResult {
  parts: ElasticsearchPart[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  aggregations?: {
    manufacturers: Array<{ key: string; doc_count: number }>;
    list_types: Array<{ key: string; doc_count: number }>;
    conditions: Array<{ key: string; doc_count: number }>;
  };
}

export class ElasticsearchService {

  // Index a single part
  async indexPart(part: ElasticsearchPart): Promise<void> {
    try {
      await esClient.index({
        index: 'parts',
        id: part.id,
        body: part
      });
    } catch (error) {
      console.error('Error indexing part:', error);
      throw error;
    }
  }

  // Index multiple parts in bulk
  async bulkIndexParts(parts: ElasticsearchPart[]): Promise<void> {
    if (parts.length === 0) return;

    try {
      const body = parts.flatMap(part => [
        { index: { _index: 'parts', _id: part.id } },
        part
      ]);

      const response = await esClient.bulk({ body });

      if (response.errors) {
        console.error('Bulk indexing errors:', response.items.filter(item => item.index?.error));
      }
    } catch (error) {
      console.error('Error bulk indexing parts:', error);
      throw error;
    }
  }

  // Fuzzy search for parts
  async fuzzySearch(params: FuzzySearchParams): Promise<SearchResult> {
    const {
      query,
      manufacturer,
      list_type,
      contact_id,
      min_price,
      max_price,
      condition,
      page = 1,
      limit = 20,
      fuzziness = 'AUTO'
    } = params;

    const from = (page - 1) * limit;

    // Build the search query
    const searchBody: any = {
      query: {
        bool: {
          must: [],
          filter: []
        }
      },
      sort: [
        { _score: { order: 'desc' } },
        { 'part_number.keyword': { order: 'asc' } }
      ],
      from,
      size: limit,
      highlight: {
        fields: {
          part_number: {},
          'part_number.ngram': {},
          description: {},
          'description.ngram': {},
          manufacturer: {}
        }
      },
      aggs: {
        manufacturers: {
          terms: { field: 'manufacturer.keyword', size: 20 }
        },
        list_types: {
          terms: { field: 'list_type', size: 10 }
        },
        conditions: {
          terms: { field: 'condition', size: 10 }
        }
      }
    };

    // Main search query with fuzzy matching
    if (query && query.trim()) {
      searchBody.query.bool.must.push({
        bool: {
          should: [
            // Exact match (highest score)
            {
              term: {
                'part_number.keyword': {
                  value: query.toUpperCase(),
                  boost: 10
                }
              }
            },
            // Fuzzy match on part number
            {
              fuzzy: {
                part_number: {
                  value: query,
                  fuzziness: fuzziness,
                  boost: 8
                }
              }
            },
            // Ngram match on part number
            {
              match: {
                'part_number.ngram': {
                  query: query,
                  boost: 6
                }
              }
            },
            // Fuzzy match on description
            {
              fuzzy: {
                description: {
                  value: query,
                  fuzziness: fuzziness,
                  boost: 4
                }
              }
            },
            // Ngram match on description
            {
              match: {
                'description.ngram': {
                  query: query,
                  boost: 3
                }
              }
            },
            // Fuzzy match on manufacturer
            {
              fuzzy: {
                manufacturer: {
                  value: query,
                  fuzziness: fuzziness,
                  boost: 2
                }
              }
            },
            // Wildcard search for partial matches
            {
              wildcard: {
                'part_number.keyword': {
                  value: `*${query.toUpperCase()}*`,
                  boost: 5
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      });
    } else {
      // If no query, match all
      searchBody.query.bool.must.push({ match_all: {} });
    }

    // Add filters
    if (manufacturer) {
      searchBody.query.bool.filter.push({
        term: { 'manufacturer.keyword': manufacturer }
      });
    }

    if (list_type) {
      searchBody.query.bool.filter.push({
        term: { list_type: list_type }
      });
    }

    if (contact_id) {
      searchBody.query.bool.filter.push({
        term: { contact_id: contact_id }
      });
    }

    if (condition) {
      searchBody.query.bool.filter.push({
        term: { condition: condition }
      });
    }

    // Price range filter
    if (min_price !== undefined || max_price !== undefined) {
      const priceFilter: any = { range: { unit_price: {} } };
      if (min_price !== undefined) priceFilter.range.unit_price.gte = min_price;
      if (max_price !== undefined) priceFilter.range.unit_price.lte = max_price;
      searchBody.query.bool.filter.push(priceFilter);
    }

    try {
      const response = await esClient.search({
        index: 'parts',
        body: searchBody
      });

      const parts = response.hits.hits.map((hit: any) => ({
        ...hit._source,
        _score: hit._score,
        _highlights: hit.highlight
      }));

      const total = typeof response.hits.total === 'object'
        ? response.hits.total.value || 0
        : response.hits.total || 0;

      return {
        parts,
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
        aggregations: {
          manufacturers: (response.aggregations?.manufacturers as any)?.buckets || [],
          list_types: (response.aggregations?.list_types as any)?.buckets || [],
          conditions: (response.aggregations?.conditions as any)?.buckets || []
        }
      };
    } catch (error) {
      console.error('Elasticsearch search error:', error);
      throw error;
    }
  }

  // Delete a part from the index
  async deletePart(partId: string): Promise<void> {
    try {
      await esClient.delete({
        index: 'parts',
        id: partId
      });
    } catch (error: any) {
      if (error.statusCode !== 404) {
        console.error('Error deleting part from index:', error);
        throw error;
      }
    }
  }

  // Delete all parts for a specific list
  async deletePartsByList(listId: string): Promise<void> {
    try {
      await esClient.deleteByQuery({
        index: 'parts',
        body: {
          query: {
            term: { list_id: listId }
          }
        }
      });
    } catch (error) {
      console.error('Error deleting parts by list:', error);
      throw error;
    }
  }

  // Refresh the index
  async refreshIndex(): Promise<void> {
    try {
      await esClient.indices.refresh({ index: 'parts' });
    } catch (error) {
      console.error('Error refreshing index:', error);
      throw error;
    }
  }
}
