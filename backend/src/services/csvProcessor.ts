import * as duckdb from 'duckdb';
import { pgPool } from '../config/database';
import path from 'path';

export interface ProcessedPart {
  part_number: string;
  description?: string;
  quantity?: number;
  unit_price?: number;
  manufacturer?: string;
  condition?: string;
  line_number: number;
}

export interface ProcessingResult {
  success: boolean;
  totalParts: number;
  processedParts: number;
  errors: string[];
  sampleData?: ProcessedPart[];
}

export interface ColumnAnalysis {
  success: boolean;
  columns: string[];
  suggestedMapping: { [key: string]: string };
  sampleRows: any[];
  totalRows: number;
  errors?: string[];
}

export class CSVProcessor {
  private db: duckdb.Database;

  constructor() {
    this.db = new duckdb.Database(':memory:');
  }

  async analyzeCSVFile(filePath: string): Promise<ColumnAnalysis> {
    return new Promise((resolve) => {
      // First, get the column structure
      this.db.all(`
        DESCRIBE SELECT * FROM read_csv_auto('${filePath}', header=true) LIMIT 1
      `, (err, columns: any[]) => {
        if (err) {
          return resolve({
            success: false,
            columns: [],
            suggestedMapping: {},
            sampleRows: [],
            totalRows: 0,
            errors: [`Failed to analyze CSV structure: ${err.message}`]
          });
        }

        const columnNames = columns.map(col => col.column_name);
        console.log('CSV columns found:', columnNames);

        // Get sample data and total count
        this.db.all(`
          SELECT * FROM read_csv_auto('${filePath}', header=true, ignore_errors=true) LIMIT 5
        `, (err2, sampleRows: any[]) => {
          if (err2) {
            return resolve({
              success: false,
              columns: columnNames,
              suggestedMapping: {},
              sampleRows: [],
              totalRows: 0,
              errors: [`Failed to read sample data: ${err2.message}`]
            });
          }

          // Get total row count
          this.db.all(`
            SELECT COUNT(*) as total FROM read_csv_auto('${filePath}', header=true, ignore_errors=true)
          `, (err3, countResult: any[]) => {
            const totalRows = err3 ? 0 : Number(countResult[0]?.total || 0);

            // Generate suggested mapping
            const suggestedMapping = this.mapColumns(columnNames.map(col => col.toLowerCase()));

            resolve({
              success: true,
              columns: columnNames,
              suggestedMapping,
              sampleRows,
              totalRows,
              errors: err3 ? [`Could not determine total rows: ${err3.message}`] : []
            });
          });
        });
      });
    });
  }

  async processCSVFileWithMapping(filePath: string, listId: string, customMapping: { [key: string]: string }): Promise<ProcessingResult> {
    return new Promise((resolve) => {
      try {
        // Process all rows and insert into PostgreSQL
        this.processAndInsertParts(filePath, listId, customMapping).then(result => {
          resolve(result);
        }).catch(error => {
          console.error('CSV processing error:', error);
          resolve({
            success: false,
            totalParts: 0,
            processedParts: 0,
            errors: [`Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
          });
        });
      } catch (error) {
        console.error('CSV processing error:', error);
        resolve({
          success: false,
          totalParts: 0,
          processedParts: 0,
          errors: [`Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
        });
      }
    });
  }

  async processCSVFile(filePath: string, listId: string): Promise<ProcessingResult> {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT * FROM read_csv_auto('${filePath}',
          header=true,
          ignore_errors=true,
          max_line_size=1048576
        ) LIMIT 10
      `, async (err, rows) => {
        if (err) {
          console.error('DuckDB CSV read error:', err);
          return resolve({
            success: false,
            totalParts: 0,
            processedParts: 0,
            errors: [`Failed to read CSV file: ${err.message}`]
          });
        }

        try {
          // Analyze the CSV structure
          const analysis = await this.analyzeCSVStructure(filePath);
          if (!analysis.success) {
            return resolve({
              success: false,
              totalParts: 0,
              processedParts: 0,
              errors: analysis.errors || ['CSV analysis failed']
            });
          }

          // Process all rows and insert into PostgreSQL
          const result = await this.processAndInsertParts(filePath, listId, analysis.columnMapping!);
          resolve(result);

        } catch (error) {
          console.error('CSV processing error:', error);
          resolve({
            success: false,
            totalParts: 0,
            processedParts: 0,
            errors: [`Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
          });
        }
      });
    });
  }

  private async analyzeCSVStructure(filePath: string): Promise<{
    success: boolean;
    columnMapping?: { [key: string]: string };
    errors?: string[];
  }> {
    return new Promise((resolve) => {
      // Get column names from the CSV
      this.db.all(`
        DESCRIBE SELECT * FROM read_csv_auto('${filePath}', header=true) LIMIT 1
      `, (err, columns: any[]) => {
        if (err) {
          return resolve({
            success: false,
            errors: [`Failed to analyze CSV structure: ${err.message}`]
          });
        }

        const columnNames = columns.map(col => col.column_name.toLowerCase());
        console.log('CSV columns found:', columnNames);

        // Map CSV columns to our database fields
        const columnMapping = this.mapColumns(columnNames);

        if (!columnMapping.part_number) {
          return resolve({
            success: false,
            errors: ['No part number column found. Expected columns like: part_number, mpn, part, partnumber, etc.']
          });
        }

        resolve({
          success: true,
          columnMapping
        });
      });
    });
  }

  private mapColumns(columnNames: string[]): { [key: string]: string } {
    const mapping: { [key: string]: string } = {};

    // Create a map of lowercase column names to original names
    const originalColumns: { [key: string]: string } = {};
    columnNames.forEach(col => {
      originalColumns[col.toLowerCase()] = col;
    });

    // Part number mapping (most important)
    const partNumberColumns = ['part_number', 'partnumber', 'part', 'mpn', 'manufacturer_part_number', 'part_no', 'partno'];
    const partCol = columnNames.find(col => partNumberColumns.some(pattern => col.toLowerCase().includes(pattern)));
    if (partCol) mapping.part_number = partCol;

    // Description mapping
    const descriptionColumns = ['description', 'desc', 'part_description', 'title', 'name', 'des'];
    const descCol = columnNames.find(col => descriptionColumns.some(pattern => col.toLowerCase().includes(pattern)));
    if (descCol) mapping.description = descCol;

    // Quantity mapping
    const quantityColumns = ['quantity', 'qty', 'stock', 'available', 'count', 'a+q'];
    const qtyCol = columnNames.find(col => quantityColumns.some(pattern => col.toLowerCase().includes(pattern)));
    if (qtyCol) mapping.quantity = qtyCol;

    // Price mapping
    const priceColumns = ['price', 'unit_price', 'unitprice', 'cost', 'amount', 'target_price', 'target price'];
    const priceCol = columnNames.find(col => priceColumns.some(pattern => col.toLowerCase().includes(pattern)));
    if (priceCol) mapping.unit_price = priceCol;

    // Manufacturer mapping
    const manufacturerColumns = ['manufacturer', 'mfg', 'brand', 'vendor', 'supplier'];
    const mfgCol = columnNames.find(col => manufacturerColumns.some(pattern => col.toLowerCase().includes(pattern)));
    if (mfgCol) mapping.manufacturer = mfgCol;

    // Condition mapping
    const conditionColumns = ['condition', 'state', 'status', 'grade'];
    const condCol = columnNames.find(col => conditionColumns.some(pattern => col.toLowerCase().includes(pattern)));
    if (condCol) mapping.condition = condCol;

    console.log('Column mapping:', mapping);
    return mapping;
  }

  private async processAndInsertParts(filePath: string, listId: string, columnMapping: { [key: string]: string }): Promise<ProcessingResult> {
    return new Promise((resolve) => {
      // Build the SELECT statement based on column mapping
      const selectFields = [];

      selectFields.push(`"${columnMapping.part_number}" as part_number`);

      if (columnMapping.description) {
        selectFields.push(`"${columnMapping.description}" as description`);
      } else {
        selectFields.push(`NULL as description`);
      }

      if (columnMapping.quantity) {
        selectFields.push(`TRY_CAST("${columnMapping.quantity}" AS INTEGER) as quantity`);
      } else {
        selectFields.push(`NULL as quantity`);
      }

      if (columnMapping.unit_price) {
        selectFields.push(`TRY_CAST("${columnMapping.unit_price}" AS DECIMAL(10,2)) as unit_price`);
      } else {
        selectFields.push(`NULL as unit_price`);
      }

      if (columnMapping.manufacturer) {
        selectFields.push(`"${columnMapping.manufacturer}" as manufacturer`);
      } else {
        selectFields.push(`NULL as manufacturer`);
      }

      if (columnMapping.condition) {
        selectFields.push(`"${columnMapping.condition}" as condition`);
      } else {
        selectFields.push(`'New' as condition`);
      }

      selectFields.push(`ROW_NUMBER() OVER() as line_number`);

      const query = `
        SELECT ${selectFields.join(', ')}
        FROM read_csv_auto('${filePath}', header=true, ignore_errors=true)
        WHERE "${columnMapping.part_number}" IS NOT NULL
        AND "${columnMapping.part_number}" != ''
      `;

      console.log('DuckDB query:', query);

      this.db.all(query, async (err, rows: any[]) => {
        if (err) {
          console.error('DuckDB processing error:', err);
          return resolve({
            success: false,
            totalParts: 0,
            processedParts: 0,
            errors: [`Failed to process CSV data: ${err.message}`]
          });
        }

        try {
          console.log(`Processing ${rows.length} parts from CSV`);

          // Insert parts into PostgreSQL in batches
          let processedCount = 0;
          const batchSize = 100;
          const errors: string[] = [];

          for (let i = 0; i < rows.length; i += batchSize) {
            const batch = rows.slice(i, i + batchSize);

            try {
              await this.insertPartsBatch(listId, batch);
              processedCount += batch.length;
            } catch (error) {
              console.error(`Error inserting batch ${i}-${i + batch.length}:`, error);
              errors.push(`Batch ${i}-${i + batch.length}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }

          // Update the list as processed
          await pgPool.query(
            'UPDATE lists SET processed = true, total_parts = $1 WHERE id = $2',
            [processedCount, listId]
          );

          resolve({
            success: true,
            totalParts: rows.length,
            processedParts: processedCount,
            errors,
            sampleData: rows.slice(0, 5) // Return first 5 rows as sample
          });

        } catch (error) {
          console.error('PostgreSQL insertion error:', error);
          resolve({
            success: false,
            totalParts: rows.length,
            processedParts: 0,
            errors: [`Database insertion failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
          });
        }
      });
    });
  }

  private async insertPartsBatch(listId: string, parts: any[]): Promise<void> {
    if (parts.length === 0) return;

    const values = parts.map((part, index) => {
      const paramOffset = index * 8;
      return `($${paramOffset + 1}, $${paramOffset + 2}, $${paramOffset + 3}, $${paramOffset + 4}, $${paramOffset + 5}, $${paramOffset + 6}, $${paramOffset + 7}, $${paramOffset + 8})`;
    }).join(', ');

    const query = `
      INSERT INTO parts (list_id, part_number, description, quantity, unit_price, manufacturer, condition, line_number)
      VALUES ${values}
    `;

    const params: any[] = [];
    parts.forEach(part => {
      params.push(
        listId,
        part.part_number || '',
        part.description || null,
        part.quantity || null,
        part.unit_price || null,
        part.manufacturer || null,
        part.condition || 'New',
        part.line_number || 1
      );
    });

    await pgPool.query(query, params);
  }

  close(): void {
    this.db.close();
  }
}
