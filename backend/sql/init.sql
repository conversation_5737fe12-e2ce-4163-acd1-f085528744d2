-- Initialize database schema for parts broker

-- Contacts table
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    company VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lists table (uploaded files)
CREATE TABLE lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contact_id UUID REFERENCES contacts(id),
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    list_type VARCHAR(20) NOT NULL CHECK (list_type IN ('offer', 'needs', 'using')),
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    total_parts INTEGER DEFAULT 0
);

-- Parts table (extracted from lists)
CREATE TABLE parts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    list_id UUID REFERENCES lists(id),
    part_number VARCHAR(255) NOT NULL,
    description TEXT,
    quantity INTEGER,
    unit_price DECIMAL(10,2),
    manufacturer VARCHAR(255),
    condition VARCHAR(50),
    line_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Price estimates table
CREATE TABLE price_estimates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    part_number VARCHAR(255) NOT NULL,
    manufacturer VARCHAR(255),
    estimated_price DECIMAL(10,2),
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    source VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Matches table (detected matches between parts)
CREATE TABLE matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_part_id UUID REFERENCES parts(id),
    target_part_id UUID REFERENCES parts(id),
    match_score DECIMAL(3,2), -- 0.00 to 1.00
    match_type VARCHAR(50), -- 'exact', 'fuzzy', 'manufacturer_match', etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_parts_part_number ON parts(part_number);
CREATE INDEX idx_parts_part_number_upper ON parts(UPPER(part_number));
CREATE INDEX idx_parts_description ON parts USING gin(to_tsvector('english', description));
CREATE INDEX idx_parts_manufacturer ON parts(manufacturer);
CREATE INDEX idx_parts_list_id ON parts(list_id);
CREATE INDEX idx_lists_contact_id ON lists(contact_id);
CREATE INDEX idx_lists_type ON lists(list_type);
CREATE INDEX idx_price_estimates_part_number ON price_estimates(part_number);
CREATE INDEX idx_matches_source_part ON matches(source_part_id);
CREATE INDEX idx_matches_target_part ON matches(target_part_id);
CREATE INDEX idx_matches_score ON matches(match_score);

-- Composite indexes for common search patterns
CREATE INDEX idx_parts_search_composite ON parts(part_number, manufacturer, list_id);
CREATE INDEX idx_lists_type_contact ON lists(list_type, contact_id);

-- Insert some sample data
INSERT INTO contacts (name, email, company) VALUES
('John Smith', '<EMAIL>', 'TechParts Inc'),
('Sarah Johnson', '<EMAIL>', 'ElectroComponents Ltd'),
('Mike Chen', '<EMAIL>', 'Semiconductor Solutions');

-- Sample price estimates
INSERT INTO price_estimates (part_number, manufacturer, estimated_price, confidence_score, source) VALUES
('STM32F407VGT6', 'STMicroelectronics', 12.50, 0.95, 'distributor_api'),
('ATMEGA328P-PU', 'Microchip', 3.25, 0.90, 'market_data'),
('LM358N', 'Texas Instruments', 0.85, 0.88, 'historical_data'),
('ESP32-WROOM-32', 'Espressif', 4.50, 0.92, 'distributor_api'),
('ARDUINO-UNO-R3', 'Arduino', 25.00, 0.85, 'market_data');

-- Sample lists (need to get contact IDs first)
DO $$
DECLARE
    contact1_id UUID;
    contact2_id UUID;
    contact3_id UUID;
    list1_id UUID;
    list2_id UUID;
    list3_id UUID;
BEGIN
    -- Get contact IDs
    SELECT id INTO contact1_id FROM contacts WHERE email = '<EMAIL>';
    SELECT id INTO contact2_id FROM contacts WHERE email = '<EMAIL>';
    SELECT id INTO contact3_id FROM contacts WHERE email = '<EMAIL>';

    -- Insert sample lists
    INSERT INTO lists (contact_id, filename, file_path, list_type, processed, total_parts) VALUES
    (contact1_id, 'microcontrollers_offer.csv', '/uploads/sample1.csv', 'offer', true, 5),
    (contact2_id, 'components_needs.csv', '/uploads/sample2.csv', 'needs', true, 3),
    (contact3_id, 'inventory_using.csv', '/uploads/sample3.csv', 'using', true, 4)
    RETURNING id INTO list1_id;

    -- Get the list IDs
    SELECT id INTO list1_id FROM lists WHERE filename = 'microcontrollers_offer.csv';
    SELECT id INTO list2_id FROM lists WHERE filename = 'components_needs.csv';
    SELECT id INTO list3_id FROM lists WHERE filename = 'inventory_using.csv';

    -- Insert sample parts
    INSERT INTO parts (list_id, part_number, description, quantity, unit_price, manufacturer, condition, line_number) VALUES
    -- List 1 (Offers)
    (list1_id, 'STM32F407VGT6', '32-bit ARM Cortex-M4 MCU, 1MB Flash', 100, 12.50, 'STMicroelectronics', 'New', 1),
    (list1_id, 'ESP32-WROOM-32', 'WiFi & Bluetooth MCU Module', 250, 4.50, 'Espressif', 'New', 2),
    (list1_id, 'ATMEGA328P-PU', '8-bit AVR MCU, 32KB Flash, DIP-28', 150, 3.25, 'Microchip', 'New', 3),
    (list1_id, 'ARDUINO-UNO-R3', 'Arduino Uno Rev3 Development Board', 50, 25.00, 'Arduino', 'New', 4),
    (list1_id, 'RASPBERRY-PI-4B', 'Raspberry Pi 4 Model B, 4GB RAM', 30, 75.00, 'Raspberry Pi Foundation', 'New', 5),

    -- List 2 (Needs)
    (list2_id, 'LM358N', 'Dual Op-Amp, DIP-8 Package', 500, 0.85, 'Texas Instruments', 'New', 1),
    (list2_id, 'STM32F407VGT6', '32-bit ARM Cortex-M4 MCU, 1MB Flash', 50, 12.00, 'STMicroelectronics', 'New', 2),
    (list2_id, '74HC595N', '8-bit Shift Register, DIP-16', 200, 0.75, 'NXP', 'New', 3),

    -- List 3 (Using)
    (list3_id, 'ESP32-WROOM-32', 'WiFi & Bluetooth MCU Module', 25, 4.50, 'Espressif', 'Used', 1),
    (list3_id, 'ATMEGA328P-PU', '8-bit AVR MCU, 32KB Flash, DIP-28', 10, 3.00, 'Microchip', 'Used', 2),
    (list3_id, 'LM358N', 'Dual Op-Amp, DIP-8 Package', 15, 0.80, 'Texas Instruments', 'Used', 3),
    (list3_id, 'ARDUINO-UNO-R3', 'Arduino Uno Rev3 Development Board', 5, 20.00, 'Arduino', 'Used', 4);

    -- Update list totals
    UPDATE lists SET total_parts = 5 WHERE id = list1_id;
    UPDATE lists SET total_parts = 3 WHERE id = list2_id;
    UPDATE lists SET total_parts = 4 WHERE id = list3_id;
END $$;
