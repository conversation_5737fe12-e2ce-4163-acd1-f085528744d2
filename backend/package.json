{"name": "parts-broker-backend", "version": "1.0.0", "description": "Electronic parts brokerage backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"@elastic/elasticsearch": "^8.11.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "duckdb": "^0.9.2", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}