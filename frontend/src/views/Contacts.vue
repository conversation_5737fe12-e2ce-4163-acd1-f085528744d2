<template>
  <div class="px-4 sm:px-0">
    <div class="sm:flex sm:items-center mb-8">
      <div class="sm:flex-auto">
        <h1 class="text-3xl font-bold text-gray-900">Contacts</h1>
        <p class="mt-2 text-gray-600">Manage your contact database</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button @click="showAddModal = true" class="btn btn-primary">
          Add Contact
        </button>
      </div>
    </div>

    <div class="card">
      <div v-if="contacts.length === 0" class="text-center py-8">
        <h3 class="mt-2 text-sm font-medium text-gray-900">No contacts</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding a new contact.</p>
        <div class="mt-6">
          <button @click="showAddModal = true" class="btn btn-primary">
            Add Contact
          </button>
        </div>
      </div>

      <div v-else class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Company
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="contact in contacts" :key="contact.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ contact.name }}</div>
                <div v-if="contact.email" class="text-sm text-gray-500">{{ contact.email }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ contact.company || '-' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Contact Modal -->
    <div v-if="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Add Contact</h3>
        <form @submit.prevent="saveContact" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Name *</label>
            <input v-model="contactForm.name" type="text" required class="mt-1 input" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Email</label>
            <input v-model="contactForm.email" type="email" class="mt-1 input" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Company</label>
            <input v-model="contactForm.company" type="text" class="mt-1 input" />
          </div>
          <div class="flex space-x-3 pt-4">
            <button type="submit" class="btn btn-primary flex-1">Add Contact</button>
            <button type="button" @click="closeModal" class="btn btn-secondary flex-1">Cancel</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Contact {
  id: string;
  name: string;
  email?: string;
  company?: string;
}

const contacts = ref<Contact[]>([])
const showAddModal = ref(false)
const contactForm = ref({
  name: '',
  email: '',
  company: ''
})

const closeModal = () => {
  showAddModal.value = false
  contactForm.value = { name: '', email: '', company: '' }
}

const saveContact = async () => {
  // TODO: Implement API call
  console.log('Saving contact:', contactForm.value)
  closeModal()
}

onMounted(() => {
  // TODO: Load contacts from API
})
</script>
