<template>
  <div class="px-4 sm:px-0">
    <div class="sm:flex sm:items-center mb-8">
      <div class="sm:flex-auto">
        <h1 class="text-3xl font-bold text-gray-900">Contacts</h1>
        <p class="mt-2 text-gray-600">Manage your contact database</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button @click="showAddModal = true" class="btn btn-primary">
          Add Contact
        </button>
      </div>
    </div>

    <!-- Error message -->
    <div v-if="error && !showAddModal" class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
      {{ error }}
    </div>

    <div class="card">
      <!-- Loading state -->
      <div v-if="loading" class="text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-2 text-sm text-gray-500">Loading contacts...</p>
      </div>

      <!-- Empty state -->
      <div v-else-if="contacts.length === 0" class="text-center py-8">
        <h3 class="mt-2 text-sm font-medium text-gray-900">No contacts</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding a new contact.</p>
        <div class="mt-6">
          <button @click="showAddModal = true" class="btn btn-primary">
            Add Contact
          </button>
        </div>
      </div>

      <div v-else class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Company
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="contact in contacts" :key="contact.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ contact.name }}</div>
                <div v-if="contact.email" class="text-sm text-gray-500">{{ contact.email }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ contact.company || '-' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Contact Modal -->
    <div v-if="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Add Contact</h3>

        <!-- Error message in modal -->
        <div v-if="error" class="mb-4 bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded text-sm">
          {{ error }}
        </div>

        <form @submit.prevent="saveContact" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Name *</label>
            <input
              v-model="contactForm.name"
              type="text"
              required
              :disabled="saving"
              class="mt-1 input disabled:opacity-50"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Email</label>
            <input
              v-model="contactForm.email"
              type="email"
              :disabled="saving"
              class="mt-1 input disabled:opacity-50"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Company</label>
            <input
              v-model="contactForm.company"
              type="text"
              :disabled="saving"
              class="mt-1 input disabled:opacity-50"
            />
          </div>
          <div class="flex space-x-3 pt-4">
            <button
              type="submit"
              :disabled="saving || !contactForm.name.trim()"
              class="btn btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="saving" class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </span>
              <span v-else>Add Contact</span>
            </button>
            <button
              type="button"
              @click="closeModal"
              :disabled="saving"
              class="btn btn-secondary flex-1 disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { contactsService, type Contact, type CreateContactRequest } from '@/services/contacts'

const contacts = ref<Contact[]>([])
const showAddModal = ref(false)
const loading = ref(false)
const saving = ref(false)
const error = ref<string | null>(null)

const contactForm = ref<CreateContactRequest>({
  name: '',
  email: '',
  company: ''
})

const closeModal = () => {
  showAddModal.value = false
  contactForm.value = { name: '', email: '', company: '' }
  error.value = null
}

const loadContacts = async () => {
  try {
    loading.value = true
    error.value = null
    contacts.value = await contactsService.getContacts()
  } catch (err) {
    console.error('Failed to load contacts:', err)
    error.value = 'Failed to load contacts. Please try again.'
  } finally {
    loading.value = false
  }
}

const saveContact = async () => {
  if (!contactForm.value.name.trim()) {
    error.value = 'Name is required'
    return
  }

  try {
    saving.value = true
    error.value = null

    const newContact = await contactsService.createContact({
      name: contactForm.value.name.trim(),
      email: contactForm.value.email?.trim() || undefined,
      company: contactForm.value.company?.trim() || undefined
    })

    // Add the new contact to the list
    contacts.value.push(newContact)

    closeModal()
  } catch (err) {
    console.error('Failed to save contact:', err)
    error.value = 'Failed to save contact. Please try again.'
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  loadContacts()
})
</script>
