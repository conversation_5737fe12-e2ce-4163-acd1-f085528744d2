<template>
  <div class="px-4 sm:px-0">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Parts Search</h1>
      <p class="mt-2 text-gray-600">Search for parts by MPN, description, or manufacturer</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <form @submit.prevent="handleSearch" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Main Search -->
          <div class="lg:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Part Number / Description</label>
            <input
              v-model="searchForm.query"
              type="text"
              placeholder="Enter MPN or description..."
              class="mt-1 input"
              :disabled="searching"
            />
          </div>

          <!-- Manufacturer Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Manufacturer</label>
            <input
              v-model="searchForm.manufacturer"
              type="text"
              placeholder="Any manufacturer"
              class="mt-1 input"
              :disabled="searching"
            />
          </div>

          <!-- List Type Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700">List Type</label>
            <select
              v-model="searchForm.listType"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              :disabled="searching"
            >
              <option value="">All Types</option>
              <option value="offer">Offers</option>
              <option value="needs">Needs</option>
              <option value="using">Using</option>
            </select>
          </div>
        </div>

        <div class="flex space-x-3">
          <button
            type="submit"
            :disabled="!searchForm.query.trim() || searching"
            class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="searching" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Searching...
            </span>
            <span v-else>Search Parts</span>
          </button>

          <button
            type="button"
            @click="clearSearch"
            :disabled="searching"
            class="btn btn-secondary disabled:opacity-50"
          >
            Clear
          </button>
        </div>
      </form>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
      {{ error }}
    </div>

    <!-- Search Results -->
    <div v-if="searchResults" class="bg-white shadow rounded-lg">
      <!-- Results Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-medium text-gray-900">
            Search Results
            <span v-if="searchResults.pagination" class="text-sm font-normal text-gray-500">
              ({{ searchResults.pagination.total }} parts found)
            </span>
          </h2>

          <!-- Results per page -->
          <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-700">Show:</label>
            <select
              v-model="searchForm.limit"
              @change="handleSearch"
              class="text-sm border-gray-300 rounded"
              :disabled="searching"
            >
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Results Table -->
      <div v-if="searchResults.parts.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Part Number
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Manufacturer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                List Type
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="part in searchResults.parts" :key="part.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ part.part_number }}</div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 max-w-xs truncate" :title="part.description">
                  {{ part.description || '-' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ part.manufacturer || '-' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ part.quantity || '-' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {{ part.unit_price ? `$${part.unit_price}` : '-' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ part.contact_name }}</div>
                <div class="text-sm text-gray-500">{{ part.contact_company || '' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="{
                        'bg-green-100 text-green-800': part.list_type === 'offer',
                        'bg-blue-100 text-blue-800': part.list_type === 'needs',
                        'bg-gray-100 text-gray-800': part.list_type === 'using'
                      }">
                  {{ part.list_type }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <div class="text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No parts found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="searchResults.pagination && searchResults.pagination.pages > 1"
           class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            Showing {{ ((searchResults.pagination.page - 1) * searchResults.pagination.limit) + 1 }} to
            {{ Math.min(searchResults.pagination.page * searchResults.pagination.limit, searchResults.pagination.total) }}
            of {{ searchResults.pagination.total }} results
          </div>

          <div class="flex space-x-2">
            <button
              @click="goToPage(searchResults.pagination.page - 1)"
              :disabled="searchResults.pagination.page <= 1 || searching"
              class="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <span class="px-3 py-1 text-sm">
              Page {{ searchResults.pagination.page }} of {{ searchResults.pagination.pages }}
            </span>

            <button
              @click="goToPage(searchResults.pagination.page + 1)"
              :disabled="searchResults.pagination.page >= searchResults.pagination.pages || searching"
              class="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { partsService, type PartsSearchParams, type PartsSearchResponse } from '@/services/parts'

const searching = ref(false)
const error = ref<string | null>(null)
const searchResults = ref<PartsSearchResponse | null>(null)

const searchForm = ref({
  query: '',
  manufacturer: '',
  listType: '',
  limit: 20,
  page: 1
})

const handleSearch = async () => {
  if (!searchForm.value.query.trim()) {
    error.value = 'Please enter a part number or description to search'
    return
  }

  try {
    searching.value = true
    error.value = null

    const params: PartsSearchParams = {
      search: searchForm.value.query.trim(),
      limit: searchForm.value.limit,
      page: searchForm.value.page
    }

    if (searchForm.value.manufacturer.trim()) {
      params.manufacturer = searchForm.value.manufacturer.trim()
    }

    if (searchForm.value.listType) {
      params.list_type = searchForm.value.listType as 'offer' | 'needs' | 'using'
    }

    searchResults.value = await partsService.searchParts(params)

  } catch (err: any) {
    console.error('Search failed:', err)
    error.value = err.response?.data?.error || 'Search failed. Please try again.'
    searchResults.value = null
  } finally {
    searching.value = false
  }
}

const clearSearch = () => {
  searchForm.value = {
    query: '',
    manufacturer: '',
    listType: '',
    limit: 20,
    page: 1
  }
  searchResults.value = null
  error.value = null
}

const goToPage = (page: number) => {
  searchForm.value.page = page
  handleSearch()
}

// Auto-search when user presses Enter
onMounted(() => {
  // Focus on search input
  const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement
  if (searchInput) {
    searchInput.focus()
  }
})
</script>
