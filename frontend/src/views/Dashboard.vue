<template>
  <div class="px-4 sm:px-0">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
      <p class="mt-2 text-gray-600">Overview of your parts brokerage activity</p>
    </div>

    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <div class="card">
        <div class="flex items-center">
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Contacts</dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ loadingStats ? '...' : contacts.length }}
              </dd>
            </dl>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">API Status</dt>
              <dd class="text-lg font-medium" :class="healthStatus === 'ok' ? 'text-green-600' : 'text-red-600'">
                {{ healthStatus === 'ok' ? 'Online' : 'Offline' }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
      <div class="space-y-3">
        <router-link to="/upload" class="btn btn-primary block text-center">
          Upload New List
        </router-link>
        <router-link to="/contacts" class="btn btn-secondary block text-center">
          Manage Contacts
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { contactsService, type Contact } from '@/services/contacts'
import { healthService } from '@/services/health'

const contacts = ref<Contact[]>([])
const loadingStats = ref(false)
const healthStatus = ref<string>('unknown')

const loadDashboardData = async () => {
  loadingStats.value = true

  try {
    // Load contacts
    const contactsData = await contactsService.getContacts()
    contacts.value = contactsData
  } catch (error) {
    console.error('Failed to load contacts:', error)
  }

  try {
    // Check API health
    const health = await healthService.checkHealth()
    healthStatus.value = health.status
  } catch (error) {
    console.error('Failed to check health:', error)
    healthStatus.value = 'error'
  }

  loadingStats.value = false
}

onMounted(() => {
  loadDashboardData()
})
</script>
