<template>
  <div class="px-4 sm:px-0">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Upload Parts List</h1>
      <p class="mt-2 text-gray-600">Upload CSV files to find matches with existing parts</p>
    </div>

    <!-- Error message -->
    <div v-if="error" class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
      {{ error }}
    </div>

    <!-- Success message -->
    <div v-if="successMessage" class="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
      {{ successMessage }}
    </div>

    <div class="max-w-2xl">
      <form @submit.prevent="handleUpload" class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700">Contact *</label>
          <select
            v-model="selectedContactId"
            :disabled="uploading || loadingContacts"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm disabled:opacity-50"
            required
          >
            <option value="">Select a contact...</option>
            <option v-for="contact in contacts" :key="contact.id" :value="contact.id">
              {{ contact.name }}{{ contact.company ? ` (${contact.company})` : '' }}
            </option>
          </select>
          <p v-if="loadingContacts" class="mt-1 text-sm text-gray-500">Loading contacts...</p>
          <p v-else-if="contacts.length === 0" class="mt-1 text-sm text-gray-500">
            No contacts available.
            <router-link to="/contacts" class="text-blue-600 hover:text-blue-500">Add a contact first</router-link>.
          </p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700">List Type *</label>
          <div class="mt-2 space-y-2">
            <div class="flex items-center">
              <input
                id="offer"
                v-model="listType"
                type="radio"
                value="offer"
                :disabled="uploading"
                class="h-4 w-4 disabled:opacity-50"
              />
              <label for="offer" class="ml-3 block text-sm text-gray-700">
                <span class="font-medium">Offer</span> - Parts to sell
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="needs"
                v-model="listType"
                type="radio"
                value="needs"
                :disabled="uploading"
                class="h-4 w-4 disabled:opacity-50"
              />
              <label for="needs" class="ml-3 block text-sm text-gray-700">
                <span class="font-medium">Needs</span> - Parts to buy
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="using"
                v-model="listType"
                type="radio"
                value="using"
                :disabled="uploading"
                class="h-4 w-4 disabled:opacity-50"
              />
              <label for="using" class="ml-3 block text-sm text-gray-700">
                <span class="font-medium">Using</span> - Parts in use
              </label>
            </div>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700">CSV File *</label>
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <div class="flex text-sm text-gray-600">
                <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                  <span>{{ uploading ? 'Uploading...' : 'Upload a file' }}</span>
                  <input
                    type="file"
                    accept=".csv,.xlsx,.xls"
                    :disabled="uploading"
                    class="sr-only"
                    @change="handleFileSelect"
                  />
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500">CSV or Excel files only (max 500MB)</p>
            </div>
          </div>
          <div v-if="selectedFile" class="mt-2 text-sm text-gray-600">
            Selected: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
          </div>

          <!-- Upload Progress Bar -->
          <div v-if="uploading" class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>Uploading...</span>
              <span>{{ uploadProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: uploadProgress + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <button
          type="submit"
          :disabled="!selectedFile || !listType || !selectedContactId || uploading"
          class="btn btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="uploading" class="flex items-center justify-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Uploading... {{ uploadProgress }}%
          </span>
          <span v-else>Upload and Process</span>
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { uploadService } from '@/services/upload'
import { contactsService, type Contact } from '@/services/contacts'

const listType = ref('')
const selectedFile = ref<File | null>(null)
const selectedContactId = ref('')
const contacts = ref<Contact[]>([])
const uploading = ref(false)
const uploadProgress = ref(0)
const loadingContacts = ref(false)
const error = ref<string | null>(null)
const successMessage = ref<string | null>(null)

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0]
    error.value = null
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const loadContacts = async () => {
  try {
    loadingContacts.value = true
    contacts.value = await contactsService.getContacts()
  } catch (err) {
    console.error('Failed to load contacts:', err)
    error.value = 'Failed to load contacts. Please try again.'
  } finally {
    loadingContacts.value = false
  }
}

const handleUpload = async () => {
  if (!selectedFile.value || !listType.value || !selectedContactId.value) return

  try {
    uploading.value = true
    uploadProgress.value = 0
    error.value = null
    successMessage.value = null

    const result = await uploadService.uploadFile(
      selectedFile.value,
      selectedContactId.value,
      listType.value as 'offer' | 'needs' | 'using',
      (progress) => {
        uploadProgress.value = progress
      }
    )

    successMessage.value = `File "${result.file.originalname}" uploaded successfully! Processing will begin shortly.`

    // Reset form
    selectedFile.value = null
    listType.value = ''
    selectedContactId.value = ''

    // Reset file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    if (fileInput) {
      fileInput.value = ''
    }

  } catch (err: any) {
    console.error('Upload failed:', err)
    error.value = err.response?.data?.error || 'Upload failed. Please try again.'
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

onMounted(() => {
  loadContacts()
})
</script>
