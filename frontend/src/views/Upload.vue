<template>
  <div class="px-4 sm:px-0">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Upload Parts List</h1>
      <p class="mt-2 text-gray-600">Upload CSV files to find matches with existing parts</p>
    </div>

    <div class="max-w-2xl">
      <form @submit.prevent="handleUpload" class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700">List Type</label>
          <div class="mt-2 space-y-2">
            <div class="flex items-center">
              <input id="offer" v-model="listType" type="radio" value="offer" class="h-4 w-4" />
              <label for="offer" class="ml-3 block text-sm text-gray-700">
                <span class="font-medium">Offer</span> - Parts to sell
              </label>
            </div>
            <div class="flex items-center">
              <input id="needs" v-model="listType" type="radio" value="needs" class="h-4 w-4" />
              <label for="needs" class="ml-3 block text-sm text-gray-700">
                <span class="font-medium">Needs</span> - Parts to buy
              </label>
            </div>
            <div class="flex items-center">
              <input id="using" v-model="listType" type="radio" value="using" class="h-4 w-4" />
              <label for="using" class="ml-3 block text-sm text-gray-700">
                <span class="font-medium">Using</span> - Parts in use
              </label>
            </div>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700">CSV File</label>
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <div class="flex text-sm text-gray-600">
                <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                  <span>Upload a file</span>
                  <input type="file" accept=".csv" class="sr-only" @change="handleFileSelect" />
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500">CSV files only</p>
            </div>
          </div>
          <div v-if="selectedFile" class="mt-2 text-sm text-gray-600">
            Selected: {{ selectedFile.name }}
          </div>
        </div>

        <button type="submit" :disabled="!selectedFile || !listType" class="btn btn-primary w-full disabled:opacity-50">
          Upload and Process
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const listType = ref('')
const selectedFile = ref<File | null>(null)

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0]
  }
}

const handleUpload = async () => {
  if (!selectedFile.value || !listType.value) return

  console.log('Uploading:', selectedFile.value.name, 'Type:', listType.value)
  // TODO: Implement upload logic
}
</script>
