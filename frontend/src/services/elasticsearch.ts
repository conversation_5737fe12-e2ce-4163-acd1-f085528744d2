import { api } from './api'

export interface FuzzySearchParams {
  query: string
  manufacturer?: string
  list_type?: 'offer' | 'needs' | 'using'
  contact_id?: string
  min_price?: number
  max_price?: number
  condition?: string
  page?: number
  limit?: number
  fuzziness?: string // 'AUTO', '0', '1', '2'
}

export interface SearchResult {
  parts: any[]
  total: number
  page: number
  limit: number
  pages: number
  aggregations?: {
    manufacturers: Array<{ key: string; doc_count: number }>
    list_types: Array<{ key: string; doc_count: number }>
    conditions: Array<{ key: string; doc_count: number }>
  }
}

export class ElasticsearchService {
  
  // Fuzzy search for parts using Elasticsearch
  async fuzzySearch(params: FuzzySearchParams): Promise<SearchResult> {
    const response = await api.get<SearchResult>('/parts', { params })
    return response.data
  }
}
