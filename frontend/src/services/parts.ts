import api from './api'

export interface Part {
  id: string
  list_id: string
  part_number: string
  description?: string
  quantity?: number
  unit_price?: number
  manufacturer?: string
  condition?: string
  line_number: number
  created_at: string
  list_type?: string
  filename?: string
  contact_name?: string
  contact_company?: string
}

export interface PartsSearchParams {
  list_id?: string
  contact_id?: string
  list_type?: 'offer' | 'needs' | 'using'
  search?: string
  manufacturer?: string
  condition?: string
  page?: number
  limit?: number
  fuzziness?: string // 'AUTO', '0', '1', '2'
}

export interface SearchAggregations {
  manufacturers: Array<{ key: string; doc_count: number }>
  list_types: Array<{ key: string; doc_count: number }>
  conditions: Array<{ key: string; doc_count: number }>
}

export interface PartsSearchResponse {
  parts: Part[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
  aggregations?: SearchAggregations
  search_type?: string
}

export const partsService = {
  // Search parts with filters
  async searchParts(params: PartsSearchParams = {}): Promise<PartsSearchResponse> {
    const response = await api.get<PartsSearchResponse>('/parts', { params })
    return response.data
  },

  // Get specific part by ID
  async getPart(id: string): Promise<Part> {
    const response = await api.get<Part>(`/parts/${id}`)
    return response.data
  },

  // Get parts for a specific list
  async getPartsByList(listId: string): Promise<Part[]> {
    const response = await api.get<PartsSearchResponse>('/parts', {
      params: { list_id: listId }
    })
    return response.data.parts
  },

  // Get parts for a specific contact
  async getPartsByContact(contactId: string): Promise<Part[]> {
    const response = await api.get<PartsSearchResponse>('/parts', {
      params: { contact_id: contactId }
    })
    return response.data.parts
  },

  // Find matches for a specific part
  async findMatches(partId: string, limit: number = 10): Promise<Part[]> {
    const response = await api.get<{ matches: Part[] }>(`/parts/${partId}/matches`, {
      params: { limit }
    })
    return response.data.matches
  }
}
