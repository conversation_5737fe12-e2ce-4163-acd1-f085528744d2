import api from './api'

export interface Contact {
  id: string
  name: string
  email?: string
  phone?: string
  company?: string
  created_at?: string
  updated_at?: string
  total_lists?: number
  total_parts?: number
}

export interface CreateContactRequest {
  name: string
  email?: string
  phone?: string
  company?: string
}

export const contactsService = {
  // Get all contacts
  async getContacts(): Promise<Contact[]> {
    const response = await api.get<Contact[]>('/contacts')
    return response.data
  },

  // Create a new contact
  async createContact(contactData: CreateContactRequest): Promise<Contact> {
    const response = await api.post<Contact>('/contacts', contactData)
    return response.data
  },

  // Get a specific contact by ID
  async getContact(id: string): Promise<Contact> {
    const response = await api.get<Contact>(`/contacts/${id}`)
    return response.data
  },

  // Update a contact
  async updateContact(id: string, contactData: Partial<CreateContactRequest>): Promise<Contact> {
    const response = await api.put<Contact>(`/contacts/${id}`, contactData)
    return response.data
  },

  // Delete a contact
  async deleteContact(id: string): Promise<void> {
    await api.delete(`/contacts/${id}`)
  }
}
