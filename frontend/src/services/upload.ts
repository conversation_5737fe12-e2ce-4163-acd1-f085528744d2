import api from './api'

export interface UploadList {
  id: string
  contact_id: string
  filename: string
  file_path: string
  list_type: 'offer' | 'needs' | 'using'
  upload_date: string
  processed: boolean
  total_parts: number
  contact_name?: string
  contact_company?: string
}

export interface UploadResponse {
  id: string
  contact_id: string
  filename: string
  file_path: string
  list_type: string
  upload_date: string
  processed: boolean
  message: string
}

export const uploadService = {
  // Upload a file
  async uploadFile(file: File, contactId: string, listType: 'offer' | 'needs' | 'using'): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('contact_id', contactId)
    formData.append('list_type', listType)

    const response = await api.post<UploadResponse>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  // Get upload history
  async getUploadHistory(): Promise<UploadList[]> {
    const response = await api.get<UploadList[]>('/upload/lists')
    return response.data
  },

  // Get specific upload details
  async getUpload(id: string): Promise<UploadList> {
    const response = await api.get<UploadList>(`/upload/${id}`)
    return response.data
  }
}
