import api from './api'

export interface UploadList {
  id: string
  contact_id: string
  filename: string
  file_path: string
  list_type: 'offer' | 'needs' | 'using'
  upload_date: string
  processed: boolean
  total_parts: number
  contact_name?: string
  contact_company?: string
}

export interface UploadResponse {
  message: string
  list: {
    id: string
    contact_id: string
    filename: string
    file_path: string
    list_type: string
    upload_date: string
    processed: boolean
    total_parts?: number
  }
  file: {
    originalname: string
    filename: string
    size: number
    mimetype: string
  }
  processing?: {
    totalParts: number
    processedParts: number
    errors: string[]
    sampleData?: any[]
  }
}

export interface AnalysisResponse {
  message: string
  list: {
    id: string
    contact_id: string
    filename: string
    file_path: string
    list_type: string
    upload_date: string
    processed: boolean
  }
  file: {
    originalname: string
    filename: string
    size: number
    mimetype: string
  }
  analysis: {
    success: boolean
    columns: string[]
    suggestedMapping: { [key: string]: string }
    sampleRows: any[]
    totalRows: number
    errors?: string[]
  }
}

export interface ProcessResponse {
  message: string
  list: {
    id: string
    contact_id: string
    filename: string
    file_path: string
    list_type: string
    upload_date: string
    processed: boolean
    total_parts?: number
  }
  processing: {
    totalParts: number
    processedParts: number
    errors: string[]
    sampleData?: any[]
  }
}

export const uploadService = {
  // Upload a file with progress tracking
  async uploadFile(
    file: File,
    contactId: string,
    listType: 'offer' | 'needs' | 'using',
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('contact_id', contactId)
    formData.append('list_type', listType)

    const response = await api.post<UploadResponse>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 600000, // 10 minutes for very large files
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
    return response.data
  },

  // Get upload history
  async getUploadHistory(): Promise<UploadList[]> {
    const response = await api.get<UploadList[]>('/upload/lists')
    return response.data
  },

  // Get specific upload details
  async getUpload(id: string): Promise<UploadList> {
    const response = await api.get<UploadList>(`/upload/lists/${id}`)
    return response.data
  },

  // Delete an upload
  async deleteUpload(id: string): Promise<void> {
    await api.delete(`/upload/lists/${id}`)
  },

  // Step 1: Analyze CSV file structure
  async analyzeFile(
    file: File,
    contactId: string,
    listType: 'offer' | 'needs' | 'using',
    onProgress?: (progress: number) => void
  ): Promise<AnalysisResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('contact_id', contactId)
    formData.append('list_type', listType)

    const response = await api.post<AnalysisResponse>('/upload/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 600000, // 10 minutes for very large files
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
    return response.data
  },

  // Step 2: Process CSV with confirmed field mapping
  async processWithMapping(
    listId: string,
    fieldMapping: { [key: string]: string }
  ): Promise<ProcessResponse> {
    const response = await api.post<ProcessResponse>(`/upload/process/${listId}`, {
      fieldMapping
    }, {
      timeout: 600000, // 10 minutes for processing
    })
    return response.data
  }
}
