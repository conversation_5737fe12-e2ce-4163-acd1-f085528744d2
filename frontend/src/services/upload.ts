import api from './api'

export interface UploadList {
  id: string
  contact_id: string
  filename: string
  file_path: string
  list_type: 'offer' | 'needs' | 'using'
  upload_date: string
  processed: boolean
  total_parts: number
  contact_name?: string
  contact_company?: string
}

export interface UploadResponse {
  message: string
  list: {
    id: string
    contact_id: string
    filename: string
    file_path: string
    list_type: string
    upload_date: string
    processed: boolean
  }
  file: {
    originalname: string
    filename: string
    size: number
    mimetype: string
  }
}

export const uploadService = {
  // Upload a file with progress tracking
  async uploadFile(
    file: File,
    contactId: string,
    listType: 'offer' | 'needs' | 'using',
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('contact_id', contactId)
    formData.append('list_type', listType)

    const response = await api.post<UploadResponse>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 600000, // 10 minutes for very large files
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
    return response.data
  },

  // Get upload history
  async getUploadHistory(): Promise<UploadList[]> {
    const response = await api.get<UploadList[]>('/upload/lists')
    return response.data
  },

  // Get specific upload details
  async getUpload(id: string): Promise<UploadList> {
    const response = await api.get<UploadList>(`/upload/lists/${id}`)
    return response.data
  },

  // Delete an upload
  async deleteUpload(id: string): Promise<void> {
    await api.delete(`/upload/lists/${id}`)
  }
}
