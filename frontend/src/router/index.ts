import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '@/views/Dashboard.vue'
import Contacts from '@/views/Contacts.vue'
import Upload from '@/views/Upload.vue'
import Search from '@/views/Search.vue'
import FuzzySearch from '@/views/FuzzySearch.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Dashboard',
      component: Dashboard
    },
    {
      path: '/contacts',
      name: 'Contacts',
      component: Contacts
    },
    {
      path: '/upload',
      name: 'Upload',
      component: Upload
    },
    {
      path: '/search',
      name: 'Search',
      component: Search
    },
    {
      path: '/fuzzy-search',
      name: 'FuzzySearch',
      component: FuzzySearch
    }
  ]
})

export default router
